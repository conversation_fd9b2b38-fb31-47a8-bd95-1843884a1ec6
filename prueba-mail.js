//Prueba de mailgun, no se utilizó por falta de dominio

import FormData from "form-data"; // form-data v4.0.1
import Mailgun from "mailgun.js"; // mailgun.js v11.1.0

async function sendSimpleMessage() {
  const mailgun = new Mailgun(FormData);
  const mg = mailgun.client({
    username: "api",
    key:
      process.env.API_KEY ||
      "**************************************************",
    // When you have an EU-domain, you must specify the endpoint:
    // url: "https://api.eu.mailgun.net"
  });
  try {
    const data = await mg.messages.create(
      "sandbox36f3626898f6415dae03626e87022d62.mailgun.org",
      {
        from: "Mailgun Sandbox <<EMAIL>>",
        to: ["Monserrat <PERSON> <<EMAIL>>"],
        subject: "Hello Mon<PERSON>rat <PERSON>",
        text: "Congratulations Mon<PERSON>rat <PERSON>, you just sent an email with <PERSON><PERSON>! You are truly awesome!",
      }
    );

    console.log(data); // logs response data
  } catch (error) {
    console.log(error); //logs any error
  }
}

// 👇 Llama la función directamente así
sendSimpleMessage();