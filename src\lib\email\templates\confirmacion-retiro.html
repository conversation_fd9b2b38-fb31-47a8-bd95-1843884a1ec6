<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solicitud de Retiro Confirmada - Backstage Música</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #f7f7f7 0%, #f0f0f0 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(103, 98, 179, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(82, 124, 235, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(16, 207, 189, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(103, 98, 179, 0.1);
            overflow: hidden;
            border: 1px solid rgba(240, 240, 240, 0.8);
            position: relative;
        }
        
        .header {
            background: linear-gradient(135deg, #10cfbd 0%, #48b0f7 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: 
                radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 40%),
                radial-gradient(circle at 70% 80%, rgba(16, 207, 189, 0.1) 0%, transparent 40%);
            animation: floatBackground 6s ease-in-out infinite;
        }
        
        @keyframes floatBackground {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(1deg); }
        }
        
        .logo {
            color: white;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
            opacity: 0.9;
        }
        
        .success-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            padding: 8px 16px;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }
        
        .success-text {
            color: white;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .main-title {
            color: white;
            font-size: 28px;
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .solicitud-id {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 600;
            font-family: 'Courier New', monospace;
            position: relative;
            z-index: 1;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
        }
        
        .content {
            padding: 40px 30px;
            position: relative;
        }
        
        .greeting {
            font-size: 16px;
            color: #374151;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .success-message {
            background: linear-gradient(135deg, rgba(16, 207, 189, 0.1) 0%, rgba(72, 176, 247, 0.1) 100%);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(16, 207, 189, 0.2);
        }
        
        .success-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #10cfbd 0%, #48b0f7 100%);
        }
        
        .success-title {
            color: #10cfbd;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .success-subtitle {
            color: #019fd2;
            font-size: 14px;
            font-weight: 500;
            position: relative;
            z-index: 1;
        }
        
        .details-card {
            background: rgba(247, 247, 247, 0.7);
            border: 2px solid rgba(103, 98, 179, 0.1);
            border-radius: 16px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .details-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #6762b3 0%, #527ceb 100%);
        }
        
        .details-title {
            color: #374151;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }
        
        .detail-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 16px 20px;
            margin-bottom: 12px;
            border: 1px solid rgba(103, 98, 179, 0.1);
            position: relative;
            z-index: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(5px);
        }
        
        .detail-item:last-child {
            margin-bottom: 0;
        }
        
        .detail-label {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
        }
        
        .detail-value {
            color: #1f2937;
            font-size: 14px;
            font-weight: 600;
        }
        
        .detail-value.monto {
            color: #10cfbd;
            font-size: 18px;
            font-weight: 700;
        }
        
        .status-card {
            background: linear-gradient(135deg, rgba(72, 176, 247, 0.1) 0%, rgba(16, 207, 189, 0.1) 100%);
            border: 2px solid rgba(72, 176, 247, 0.2);
            border-radius: 16px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }
        
        .status-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #48b0f7 0%, #10cfbd 100%);
        }
        
        .status-title {
            color: #019fd2;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .status-value {
            color: #48b0f7;
            font-size: 20px;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }
        
        .process-card {
            background: rgba(16, 207, 189, 0.05);
            border: 1px solid rgba(16, 207, 189, 0.2);
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            backdrop-filter: blur(5px);
        }
        
        .process-title {
            color: #019fd2;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .process-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .process-steps li {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 10px;
            padding-left: 30px;
            position: relative;
        }
        
        .process-steps li::before {
            content: attr(data-step);
            position: absolute;
            left: 0;
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #48b0f7, #10cfbd);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #6762b3 0%, #527ceb 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(103, 98, 179, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(103, 98, 179, 0.4);
        }
        
        .cta-button:hover::before {
            left: 100%;
        }
        
        .footer {
            background: rgba(247, 247, 247, 0.8);
            padding: 30px;
            text-align: center;
            border-top: 1px solid rgba(240, 240, 240, 0.8);
            backdrop-filter: blur(10px);
        }
        
        .footer-text {
            color: #64748b;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .company-name {
            color: #6762b3;
            font-weight: 600;
        }
        
        .divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(103, 98, 179, 0.2), transparent);
            margin: 30px 0;
        }
        
        @media (max-width: 640px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .main-title {
                font-size: 24px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .details-card, .status-card {
                padding: 25px 20px;
            }
            
            .footer {
                padding: 25px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">Backstage Música</div>
            <div class="success-badge">
                <span class="success-text">Confirmación</span>
            </div>
            <h1 class="main-title">Solicitud de Retiro Confirmada</h1>
            <div class="solicitud-id">ID: #{{solicitudId}}</div>
        </div>
        
        <div class="content">
            <p class="greeting">
                <strong>Hola {{nombreArtista}},</strong><br><br>
                Hemos recibido correctamente tu solicitud de retiro. Te mantendremos informado sobre el progreso de tu petición.
            </p>
            
            <div class="success-message">
                <div class="success-title">Solicitud Recibida Exitosamente</div>
                <div class="success-subtitle">Tu retiro está siendo procesado por nuestro equipo</div>
            </div>
            
            <div class="details-card">
                <h3 class="details-title">Detalles de tu Solicitud</h3>
                <div class="detail-item">
                    <span class="detail-label">ID de Solicitud</span>
                    <span class="detail-value">{{solicitudId}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Monto Solicitado</span>
                    <span class="detail-value monto">${{monto}} USD</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Fecha de Solicitud</span>
                    <span class="detail-value">{{fecha}}</span>
                </div>
            </div>
            
            <div class="status-card">
                <div class="status-title">Estado Actual</div>
                <div class="status-value">Pendiente de Revisión</div>
            </div>
            
            <div class="process-card">
                <h4 class="process-title">Proceso de tu Retiro</h4>
                <ul class="process-steps">
                    <li data-step="1"><strong>Pendiente</strong> - Revisión inicial de tu solicitud</li>
                    <li data-step="2"><strong>Procesando</strong> - Validación y aprobación</li>
                    <li data-step="3"><strong>Completado</strong> - Pago enviado a tu cuenta</li>
                </ul>
            </div>
            
            <p style="color: #374151; font-size: 16px; line-height: 1.6; margin: 30px 0; text-align: center;">
                Puedes seguir el progreso de tu solicitud en tu panel de artista:
            </p>
            
            <div style="text-align: center;">
                <a href="{{urlPanelArtista}}" class="cta-button">Ver Mi Panel</a>
            </div>
            
            <div class="divider"></div>
            
            <p style="color: #64748b; font-size: 15px; line-height: 1.6; text-align: center;">
                <strong>Te notificaremos automáticamente por email</strong><br>
                cuando tu solicitud cambie de estado.
            </p>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Si tienes alguna pregunta sobre tu retiro,<br>
                no dudes en contactar a nuestro equipo de soporte.
            </p>
            <p class="footer-text">
                <strong>Gracias por confiar en nosotros,</strong><br>
                <span class="company-name">El equipo de Backstage Música</span>
            </p>
        </div>
    </div>
</body>
</html>