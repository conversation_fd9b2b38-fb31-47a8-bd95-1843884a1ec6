// src/lib/email/templateEngine.ts
import { readFileSync } from 'fs';
import path from 'path';

/**
 * Reemplazo simple de Handlebars compatible con Next.js 15
 * Soporta variables {{variable}} y condicionales básicos
 */

// Helper para comparar strings (reemplaza el helper de Handlebars)
function eq(a: any, b: any): boolean {
  return a === b;
}

/**
 * Procesa condicionales simples en formato: {{#if (eq variable "valor")}}...{{/if}}
 */
function processConditionals(template: string, data: Record<string, any>): string {
  let result = template;
  
  // Regex para encontrar bloques condicionales
  const conditionalRegex = /\{\{#if\s+\(eq\s+(\w+)\s+"([^"]+)"\)\}\}([\s\S]*?)\{\{\/if\}\}/g;
  
  result = result.replace(conditionalRegex, (match, variable, value, content) => {
    const variableValue = data[variable];
    return eq(variableValue, value) ? content : '';
  });
  
  return result;
}

/**
 * Reemplaza variables simples en formato: {{variable}}
 */
function processVariables(template: string, data: Record<string, any>): string {
  let result = template;
  
  // Reemplazar variables simples {{variable}}
  Object.keys(data).forEach(key => {
    const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
    const value = data[key] !== undefined && data[key] !== null ? String(data[key]) : '';
    result = result.replace(regex, value);
  });
  
  return result;
}

/**
 * Renderiza un template HTML usando nuestro sistema personalizado.
 * Compatible con sintaxis básica de Handlebars.
 * @param templateName - Nombre del archivo de la plantilla (sin .html).
 * @param data - Objeto con los datos para la plantilla.
 * @returns El HTML renderizado.
 */
export function renderTemplate(templateName: string, data: Record<string, any>): string {
  try {
    const templatePath = path.join(process.cwd(), 'src', 'lib', 'email', 'templates', `${templateName}.html`);
    let source: string;
    
    try {
      source = readFileSync(templatePath, 'utf-8');
    } catch (fileError) {
      // Si no se encuentra el archivo, usar template básico
      console.warn(`⚠️ [TEMPLATE] No se encontró ${templatePath}, usando template básico`);
      source = getBasicTemplate(templateName, data);
    }

    // Procesar condicionales primero
    let result = processConditionals(source, data);
    
    // Luego procesar variables simples
    result = processVariables(result, data);
    
    console.log(`✅ [TEMPLATE] Plantilla '${templateName}' renderizada exitosamente con sistema personalizado.`);
    
    return result;

  } catch (error: any) {
    console.error(`❌ [TEMPLATE ERROR] Error renderizando plantilla '${templateName}':`, error);
    
    // Devolver un HTML de fallback en caso de error.
    return getErrorTemplate(templateName, error.message);
  }
}

/**
 * Template básico cuando no se encuentra el archivo
 */
function getBasicTemplate(templateName: string, data: Record<string, any>): string {
  switch (templateName) {
    case 'retiro-solicitado':
      return `
        <html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Solicitud de Retiro Recibida</h2>
          <p>Hola {{nombreArtista}},</p>
          <p>Hemos recibido tu solicitud de retiro por <strong>${{monto}}</strong>.</p>
          <p>ID de solicitud: {{solicitudId}}</p>
          <p>Te notificaremos cuando sea procesada.</p>
          <hr>
          <p style="color: #666; font-size: 12px;">Sistema de Pagos</p>
        </body></html>
      `;
      
    case 'retiro-completado':
      return `
        <html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #16a34a;">Retiro Completado</h2>
          <p>Hola {{nombreArtista}},</p>
          <p>Tu retiro por <strong>${{monto}}</strong> ha sido completado exitosamente.</p>
          <p>ID de solicitud: {{solicitudId}}</p>
          <p>Fecha: {{fechaCompletado}}</p>
          <hr>
          <p style="color: #666; font-size: 12px;">Sistema de Pagos</p>
        </body></html>
      `;
      
    case 'alerta-admin':
      return `
        <html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #dc2626;">Nueva Solicitud de Retiro</h2>
          <p>Hola {{nombreAdmin}},</p>
          <p>Nueva solicitud de retiro de {{nombreArtista}} por <strong>${{monto}}</strong>.</p>
          <p>Criterio de alerta: {{criterioAlerta}}</p>
          <p><a href="{{urlPanelAdmin}}" style="background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Ver en Panel</a></p>
          <hr>
          <p style="color: #666; font-size: 12px;">Sistema de Pagos</p>
        </body></html>
      `;
      
    default:
      return `
        <html><body style="font-family: Arial, sans-serif;">
          <h2>Notificación del Sistema</h2>
          <p>Se ha generado una notificación del sistema de pagos.</p>
          <p>Detalles: {{mensaje}}</p>
        </body></html>
      `;
  }
}

/**
 * Template de error
 */
function getErrorTemplate(templateName: string, errorMessage: string): string {
  return `
    <html><body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #dc2626;">Error en el sistema de plantillas</h1>
      <p>No se pudo cargar la plantilla: <strong>${templateName}</strong></p>
      <p>Error: ${errorMessage}</p>
      <hr>
      <p style="color: #666; font-size: 12px;">Sistema de Pagos - Notificación Automática</p>
    </body></html>
  `;
}

/**
 * Función auxiliar para debug - muestra qué variables están disponibles.
 */
export function debugTemplate(templateName: string, data: Record<string, any>): void {
  console.log(`🔍 [TEMPLATE DEBUG] Plantilla: ${templateName}`);
  console.log('📋 Variables disponibles:', Object.keys(data));
  console.log('📄 Datos completos:', JSON.stringify(data, null, 2));
}