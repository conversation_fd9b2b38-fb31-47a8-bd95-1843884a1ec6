{"name": "system-finance", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "next lint", "postinstall": "prisma generate", "seed": "tsx prisma/seed.ts"}, "dependencies": {"@chakra-ui/react": "^3.21.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@heroicons/react": "^2.2.0", "@material-tailwind/react": "^2.1.10", "@prisma/client": "^6.11.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.1.1", "@sendgrid/mail": "^8.1.5", "@table-library/react-table-library": "^4.1.15", "@tailwindcss/cli": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@types/styled-components": "^5.1.34", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "form-data": "^4.0.3", "framer-motion": "^12.23.3", "handlebars": "^4.7.8", "input-otp": "^1.4.2", "iron-session": "^8.0.4", "jose": "^6.0.11", "lucide-react": "^0.525.0", "mailgun.js": "^12.0.3", "mysql2": "^3.14.1", "nanoid": "^5.1.5", "next": "^15.4.5", "next-themes": "^0.4.6", "nodemailer": "^7.0.4", "prisma": "^6.11.1", "react": "^18.3.1", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "react-resizable-panels": "^3.0.4", "sonner": "^2.0.6", "styled-components": "^6.1.19", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "dotenv": "^17.0.1", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "ts-node": "^10.9.2", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}