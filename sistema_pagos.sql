-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: 127.0.0.1:3306
-- Tiempo de generación: 30-07-2025 a las 19:17:05
-- Versión del servidor: 8.0.42
-- Versión de PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `sistema_pagos`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `admin_artista_relacion`
--

CREATE TABLE `admin_artista_relacion` (
  `admin_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `artista_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `admin_artista_relacion`
--

INSERT INTO `admin_artista_relacion` (`admin_id`, `artista_id`) VALUES
('4bc7cdd7-b310-4ace-8824-69873e5cde9d', '0971c0dd-87e2-44c8-9b4e-5df1cfc83613'),
('4bc7cdd7-b310-4ace-8824-69873e5cde9d', '93187b5b-7aba-41c4-bae1-a43dd54c4f5e');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `alertas`
--

CREATE TABLE `alertas` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `retiro_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tipo` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mensaje` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `resuelta` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `alertas`
--

INSERT INTO `alertas` (`id`, `retiro_id`, `tipo`, `mensaje`, `resuelta`, `createdAt`, `updatedAt`) VALUES
('08712a06-83a9-45b3-85ca-28800fc016bf', '01b8ecae-a5c3-4bfb-974d-bb67bca20537', 'PATRON_SOSPECHOSO', 'Múltiples retiros en período corto', 0, '2025-07-29 07:54:10.815', '2025-07-29 07:54:10.815'),
('a5baae0b-66eb-4693-bedb-81112d1d11f5', '01b8ecae-a5c3-4bfb-974d-bb67bca20537', 'RETIROS_MULTIPLES', '4° retiro del mes', 0, '2025-07-29 07:54:10.805', '2025-07-29 07:54:10.805'),
('cb34b25d-80e1-4730-82ea-efe1e15c037b', '01b8ecae-a5c3-4bfb-974d-bb67bca20537', 'CUENTA_NUEVA', 'Cuenta bancaria agregada recientemente', 0, '2025-07-29 07:54:10.820', '2025-07-29 07:54:10.820');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cuentas_bancarias`
--

CREATE TABLE `cuentas_bancarias` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `usuario_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tipo_cuenta` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `nombre_banco` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nombre_titular` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `es_predeterminada` tinyint(1) NOT NULL DEFAULT '0',
  `clabe` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tipo_cuenta_nacional` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `numero_ruta` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `numero_cuenta` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `swift` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `codigo_aba` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tipo_cuenta_internacional` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pais` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `direccion_beneficiario` text COLLATE utf8mb4_unicode_ci,
  `ciudad_beneficiario` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `estado_beneficiario` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `codigo_postal_beneficiario` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pais_beneficiario` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `direccion_banco` text COLLATE utf8mb4_unicode_ci,
  `ciudad_banco` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `estado_banco` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `codigo_postal_banco` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pais_banco` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_paypal` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `cuentas_bancarias`
--

INSERT INTO `cuentas_bancarias` (`id`, `usuario_id`, `tipo_cuenta`, `nombre_banco`, `nombre_titular`, `es_predeterminada`, `clabe`, `tipo_cuenta_nacional`, `numero_ruta`, `numero_cuenta`, `swift`, `codigo_aba`, `tipo_cuenta_internacional`, `pais`, `direccion_beneficiario`, `ciudad_beneficiario`, `estado_beneficiario`, `codigo_postal_beneficiario`, `pais_beneficiario`, `direccion_banco`, `ciudad_banco`, `estado_banco`, `codigo_postal_banco`, `pais_banco`, `email_paypal`, `createdAt`, `updatedAt`) VALUES
('02ecb2ce-fe33-45bd-94e4-7ec7b3a93481', '489dd900-5d2c-46fb-8cfa-14f64813b246', 'internacional', 'UPDATE nombreB', 'John Smith UPDATE', 1, NULL, NULL, NULL, '123456789013', 'WFBIUS6SXXO', '121000123', 'checking', 'US', '742 Evergreen Terrace', 'Cambio Para Update Ciudad', 'Illinois', '62704', 'ESTADOS UNIDOS', 'Cambio UPDATE direccionB', 'San Francisco', 'California', '7777', 'USA', NULL, '2025-07-23 17:49:25.510', '2025-07-23 20:02:23.765'),
('53bf08e9-a590-47ba-bcef-3f17dace979c', '489dd900-5d2c-46fb-8cfa-14f64813b246', 'nacional', 'Santander México', 'Monserrat Caballero López', 0, '014320001234567890', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-23 07:33:14.346', '2025-07-23 20:02:23.732'),
('60a9e740-7f1a-4cce-b4fb-083c1b5cbcb4', '489dd900-5d2c-46fb-8cfa-14f64813b246', 'internacional', 'Wells Fargo Bank', 'Monserrat Caballero Lopez', 0, NULL, NULL, NULL, 'US1234567890123456', 'WFBIUS6S', '*********', NULL, 'USA', '1234 Main Street Apt 5B', 'Los Angeles', 'CA', '90210', 'USA', NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-23 07:33:14.357', '2025-07-23 20:02:23.732'),
('69af9af6-c7b3-4554-b688-85f5f6bb0531', '489dd900-5d2c-46fb-8cfa-14f64813b246', 'paypal', NULL, 'Monserrat Caballero', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '<EMAIL>', '2025-07-23 07:33:14.368', '2025-07-23 20:02:23.732');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `notas_usuario`
--

CREATE TABLE `notas_usuario` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `usuario_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `admin_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `nota` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `retiros`
--

CREATE TABLE `retiros` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `usuario_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cuenta_bancaria_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `monto_solicitado` decimal(10,2) NOT NULL,
  `estado` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Pendiente',
  `url_comprobante` text COLLATE utf8mb4_unicode_ci,
  `notas_admin` text COLLATE utf8mb4_unicode_ci,
  `fecha_solicitud` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `fecha_actualizacion` datetime(3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `retiros`
--

INSERT INTO `retiros` (`id`, `usuario_id`, `cuenta_bancaria_id`, `monto_solicitado`, `estado`, `url_comprobante`, `notas_admin`, `fecha_solicitud`, `fecha_actualizacion`) VALUES
('01b8ecae-a5c3-4bfb-974d-bb67bca20537', '489dd900-5d2c-46fb-8cfa-14f64813b246', '69af9af6-c7b3-4554-b688-85f5f6bb0531', 100.00, 'Pendiente', NULL, NULL, '2025-07-29 07:54:10.755', '2025-07-29 07:54:10.755'),
('4772c572-4a64-4e29-8f80-287e7f076730', '489dd900-5d2c-46fb-8cfa-14f64813b246', '69af9af6-c7b3-4554-b688-85f5f6bb0531', 100.00, 'Completado', 'uploads/comprobantes/comprobante_4772c572-4a64-4e29-8f80-287e7f076730_1753385077213.png', NULL, '2025-07-24 19:22:31.644', '2025-07-24 19:24:37.219'),
('c72448c6-23b6-4527-a877-4f5fcae368be', '489dd900-5d2c-46fb-8cfa-14f64813b246', '60a9e740-7f1a-4cce-b4fb-083c1b5cbcb4', 200.00, 'Rechazado', NULL, 'no quiero bitch', '2025-07-24 20:07:22.196', '2025-07-24 20:23:13.563'),
('e1d0b6b4-9b12-4114-9abd-2fdf531f0e6f', '489dd900-5d2c-46fb-8cfa-14f64813b246', '02ecb2ce-fe33-45bd-94e4-7ec7b3a93481', 200.00, 'Completado', 'uploads/comprobantes/comprobante_e1d0b6b4-9b12-4114-9abd-2fdf531f0e6f_1753388627473.png', 'no', '2025-07-24 20:18:53.999', '2025-07-24 20:23:47.476'),
('ed4056e0-73b2-4f38-9c13-bbea779f3599', '489dd900-5d2c-46fb-8cfa-14f64813b246', '69af9af6-c7b3-4554-b688-85f5f6bb0531', 100.00, 'Completado', 'uploads/comprobantes/comprobante_ed4056e0-73b2-4f38-9c13-bbea779f3599_1753436943739.pdf', 'hola', '2025-07-25 09:45:33.994', '2025-07-25 09:49:03.745');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `tokens_recuperacion`
--

CREATE TABLE `tokens_recuperacion` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `usuario_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `fecha_expiracion` datetime(3) NOT NULL,
  `usado` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuarios`
--

CREATE TABLE `usuarios` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `nombre_completo` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password_hash` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `rol` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `estado_cuenta` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Activa',
  `requiere_cambio_password` tinyint(1) NOT NULL DEFAULT '0',
  `createdAt` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` datetime(3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `usuarios`
--

INSERT INTO `usuarios` (`id`, `nombre_completo`, `email`, `password_hash`, `rol`, `estado_cuenta`, `requiere_cambio_password`, `createdAt`, `updatedAt`) VALUES
('0971c0dd-87e2-44c8-9b4e-5df1cfc83613', 'Sele', '<EMAIL>', '$2b$10$c2XRPxUcfdcxatofhvUxneP.z.HR1SwUa5E4XxnT6.7k09VGjMce2', 'artista', 'Activa', 0, '2025-07-29 00:31:33.744', '2025-07-29 00:34:20.599'),
('489dd900-5d2c-46fb-8cfa-14f64813b246', 'moncaballero1710', '<EMAIL>', '$2b$10$1PMaoRAnzDmBfIXvq7v7gewStkOAq5oTtkGQYpcVD0lDYBrkH4tQW', 'artista', 'Activa', 0, '2025-07-15 19:00:03.508', '2025-07-15 19:07:21.221'),
('4bc7cdd7-b310-4ace-8824-69873e5cde9d', 'pipi', '<EMAIL>', '$2b$10$B8fU8bwEInkKTq4pnKfcfe5ZBoF1bwnD.LxW9SMU82TQeKemI9I0S', 'admin', 'Activa', 0, '2025-07-15 05:55:22.469', '2025-07-15 05:56:51.439'),
('93187b5b-7aba-41c4-bae1-a43dd54c4f5e', 'Mario Montalvo', '<EMAIL>', '$2b$10$9WiiWvYgLrb4m2m1kJfcqO9D2Aer09popMgJ4Mchbc75q2/WXehqq', 'artista', 'Activa', 0, '2025-07-28 18:05:56.947', '2025-07-28 18:25:01.566');

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `admin_artista_relacion`
--
ALTER TABLE `admin_artista_relacion`
  ADD PRIMARY KEY (`admin_id`,`artista_id`),
  ADD KEY `admin_artista_relacion_artista_id_fkey` (`artista_id`);

--
-- Indices de la tabla `alertas`
--
ALTER TABLE `alertas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `alertas_retiro_id_fkey` (`retiro_id`);

--
-- Indices de la tabla `cuentas_bancarias`
--
ALTER TABLE `cuentas_bancarias`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cuentas_bancarias_usuario_id_clabe_key` (`usuario_id`,`clabe`),
  ADD UNIQUE KEY `cuentas_bancarias_usuario_id_numero_ruta_numero_cuenta_key` (`usuario_id`,`numero_ruta`,`numero_cuenta`),
  ADD UNIQUE KEY `cuentas_bancarias_usuario_id_email_paypal_key` (`usuario_id`,`email_paypal`);

--
-- Indices de la tabla `notas_usuario`
--
ALTER TABLE `notas_usuario`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notas_usuario_admin_id_fkey` (`admin_id`),
  ADD KEY `notas_usuario_usuario_id_fkey` (`usuario_id`);

--
-- Indices de la tabla `retiros`
--
ALTER TABLE `retiros`
  ADD PRIMARY KEY (`id`),
  ADD KEY `retiros_usuario_id_fkey` (`usuario_id`),
  ADD KEY `retiros_cuenta_bancaria_id_fkey` (`cuenta_bancaria_id`);

--
-- Indices de la tabla `tokens_recuperacion`
--
ALTER TABLE `tokens_recuperacion`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tokens_recuperacion_token_key` (`token`),
  ADD KEY `tokens_recuperacion_usuario_id_fkey` (`usuario_id`);

--
-- Indices de la tabla `usuarios`
--
ALTER TABLE `usuarios`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `usuarios_email_key` (`email`);

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `admin_artista_relacion`
--
ALTER TABLE `admin_artista_relacion`
  ADD CONSTRAINT `admin_artista_relacion_admin_id_fkey` FOREIGN KEY (`admin_id`) REFERENCES `usuarios` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `admin_artista_relacion_artista_id_fkey` FOREIGN KEY (`artista_id`) REFERENCES `usuarios` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `alertas`
--
ALTER TABLE `alertas`
  ADD CONSTRAINT `alertas_retiro_id_fkey` FOREIGN KEY (`retiro_id`) REFERENCES `retiros` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `cuentas_bancarias`
--
ALTER TABLE `cuentas_bancarias`
  ADD CONSTRAINT `cuentas_bancarias_usuario_id_fkey` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `notas_usuario`
--
ALTER TABLE `notas_usuario`
  ADD CONSTRAINT `notas_usuario_admin_id_fkey` FOREIGN KEY (`admin_id`) REFERENCES `usuarios` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `notas_usuario_usuario_id_fkey` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `retiros`
--
ALTER TABLE `retiros`
  ADD CONSTRAINT `retiros_cuenta_bancaria_id_fkey` FOREIGN KEY (`cuenta_bancaria_id`) REFERENCES `cuentas_bancarias` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `retiros_usuario_id_fkey` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `tokens_recuperacion`
--
ALTER TABLE `tokens_recuperacion`
  ADD CONSTRAINT `tokens_recuperacion_usuario_id_fkey` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
