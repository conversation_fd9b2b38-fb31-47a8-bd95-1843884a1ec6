<!DOCTYPE html>
<html lang="es" style="box-sizing: border-box; margin: 0; padding: 0;">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>¡Bienvenido a Backstage Música!</title>

    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            padding: 20px;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewbox="0 0 100 100"><defs><radialgradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.1)"></stop><stop offset="100%" style="stop-color:rgba(255,255,255,0)"></stop></radialgradient></defs><circle cx="50" cy="50" r="50" fill="url(%23a)"></circle></svg>');
            opacity: 0.3;
        }

        .credentials-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .cta-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
            background: #2a2a2a;
        }

        @media (max-width: 640px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 30px 20px;
            }

            .main-title {
                font-size: 28px;
            }

            .content {
                padding: 30px 20px;
            }

            .credentials-card {
                padding: 25px 20px;
            }

            .footer {
                padding: 25px 20px;
            }
        }
    </style>
</head>

<body
    style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; min-height: 100vh; margin: 0; padding: 20px;"
    bgcolor="#f8fafc">
    <div class="email-container"
        style="box-sizing: border-box; max-width: 600px; background-color: white; border-radius: 16px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); overflow: hidden; margin: 0 auto; padding: 0; border: 1px solid #e2e8f0;">
        <div class="header"
            style="box-sizing: border-box; background-image: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; margin: 0; padding: 40px 30px;"
            align="center">
            <div class="logo"
                style="box-sizing: border-box; color: white; font-size: 14px; font-weight: 500; position: relative; z-index: 1; margin: 0 0 20px; padding: 0;">
                Backstage Música</div>
            <h1 class="main-title"
                style="box-sizing: border-box; color: white; font-size: 28px; font-weight: 600; line-height: 1.2; position: relative; z-index: 1; margin: 0 0 15px; padding: 0;">
                Bienvenido a Backstage Música</h1>
            <p class="subtitle"
                style="box-sizing: border-box; color: rgba(255, 255, 255, 0.9); font-size: 16px; font-weight: 400; line-height: 1.5; position: relative; z-index: 1; margin: 0; padding: 0;">
                Tu plataforma exclusiva para gestionar tus finanzas y retiros con total transparencia y seguridad.</p>
        </div>

        <div class="content" style="box-sizing: border-box; margin: 0; padding: 40px 30px;">
            <p class="welcome-text"
                style="box-sizing: border-box; font-size: 16px; color: #374151; line-height: 1.6; margin: 0 0 30px; padding: 0;">
                <strong style="box-sizing: border-box; margin: 0; padding: 0;">¡Hola!</strong><br
                    style="box-sizing: border-box; margin: 0; padding: 0;"><br
                    style="box-sizing: border-box; margin: 0; padding: 0;">
                Te damos la más cordial bienvenida a nuestra plataforma. Estamos muy contentos de tenerte con nosotros y
                esperamos que disfrutes de una experiencia excepcional gestionando tus pagos.
            </p>

            <!-- Credenciales de Acceso -->
            <div
                style="background-color: #f1f5f9; border: 1px solid #e2e8f0; border-radius: 12px; padding: 24px 20px; margin: 30px 0;">

                <h3 style="font-size: 16px; font-weight: 600; color: #1e293b; margin: 0 0 20px;">
                    🔐 Credenciales de Acceso
                </h3>

                <!-- Email -->
                <div style="margin-bottom: 15px;">
                    <div style="font-size: 13px; color: #64748b; font-weight: 500; margin-bottom: 4px;">
                        Email
                    </div>
                    <div
                        style="font-family: 'Courier New', monospace; font-size: 15px; color: #0f172a; background-color: #e2e8f0; padding: 10px 14px; border-radius: 8px; border: 1px solid #cbd5e1;">
                        {{email}}
                    </div>
                </div>

                <!-- Contraseña -->
                <div>
                    <div style="font-size: 13px; color: #64748b; font-weight: 500; margin-bottom: 4px;">
                        Contraseña Temporal
                    </div>
                    <div
                        style="font-family: 'Courier New', monospace; font-size: 15px; color: #0f172a; background-color: #e2e8f0; padding: 10px 14px; border-radius: 8px; border: 1px solid #cbd5e1;">
                        {{password}}
                    </div>
                </div>
            </div>


            <div style="box-sizing: border-box; margin: 0; padding: 0;" align="center">
                <a href="%7B%7BloginUrl%7D%7D" class="cta-button"
                    style="box-sizing: border-box; display: inline-block; background-color: #1a1a1a; color: white; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 15px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease; margin: 25px 0; padding: 14px 28px;">Acceder
                    a mi Cuenta</a>
            </div>

            <div class="security-notice"
                style="box-sizing: border-box; background-color: rgba(255, 193, 7, 0.1); border-radius: 12px; margin: 30px 0; padding: 20px; border: 1px solid rgba(255,193,7,0.3);">
                <span class="icon"
                    style="box-sizing: border-box; display: inline-block; width: 20px; height: 20px; vertical-align: middle; margin: 0 10px 0 0; padding: 0;">🛡️</span>
                <span class="security-text"
                    style="box-sizing: border-box; color: #b45309; font-size: 15px; line-height: 1.5; font-weight: 500; margin: 0; padding: 0;">
                    <strong style="box-sizing: border-box; margin: 0; padding: 0;">Importante:</strong> Por tu
                    seguridad, te recomendamos encarecidamente cambiar tu contraseña tan pronto como inicies sesión por
                    primera vez. Tu cuenta está protegida, pero una contraseña personalizada añade una capa extra de
                    seguridad.
                </span>
            </div>

            <div class="divider"
                style="box-sizing: border-box; height: 1px; background-image: linear-gradient(90deg, transparent, #e2e8f0, transparent); margin: 30px 0; padding: 0;">
            </div>

            <p
                style="color: #64748b; font-size: 15px; line-height: 1.6; box-sizing: border-box; margin: 0; padding: 0;">
                <strong style="box-sizing: border-box; margin: 0; padding: 0;">¿Qué puedes hacer ahora?</strong><br
                    style="box-sizing: border-box; margin: 0; padding: 0;">
                • Configura tus cuentas bancarias de forma segura<br
                    style="box-sizing: border-box; margin: 0; padding: 0;">
                • Solicita retiros rápidos y verificados<br style="box-sizing: border-box; margin: 0; padding: 0;">
                • Monitorea el estado de tus pagos en tiempo real<br
                    style="box-sizing: border-box; margin: 0; padding: 0;">
                • Accede a tu historial completo de transacciones
            </p>
        </div>

        <div class="footer"
            style="box-sizing: border-box; background-color: #f8fafc; border-top-width: 1px; border-top-color: #e2e8f0; border-top-style: solid; margin: 0; padding: 30px;"
            align="center">
            <p class="footer-text"
                style="box-sizing: border-box; color: #64748b; font-size: 14px; line-height: 1.6; margin: 0 0 15px; padding: 0;">
                Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br
                    style="box-sizing: border-box; margin: 0; padding: 0;">
                Estamos aquí para apoyarte en cada paso del camino.
            </p>
            <p class="footer-text"
                style="box-sizing: border-box; color: #64748b; font-size: 14px; line-height: 1.6; margin: 0 0 15px; padding: 0;">
                <strong style="box-sizing: border-box; margin: 0; padding: 0;">Saludos cordiales,</strong><br
                    style="box-sizing: border-box; margin: 0; padding: 0;">
                <span class="company-name"
                    style="box-sizing: border-box; color: #667eea; font-weight: 600; margin: 0; padding: 0;">El equipo
                    de Backstage Música</span>
            </p>
        </div>
    </div>
</body>

</html>