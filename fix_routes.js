const fs = require('fs');
const path = require('path');

function fixRouteFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let modified = false;

        // Patrón 1: { params }: { params: { id: string } }
        const pattern1 = /{\s*params\s*}:\s*{\s*params:\s*{\s*id:\s*string\s*}\s*}/g;
        if (pattern1.test(content)) {
            content = content.replace(pattern1, '{ params }: { params: Promise<{ id: string }> }');
            modified = true;
        }

        // Patrón 2: const { id } = params;
        const pattern2 = /const\s*{\s*id\s*}\s*=\s*params;/g;
        if (pattern2.test(content)) {
            content = content.replace(pattern2, 'const { id } = await params;');
            modified = true;
        }

        if (modified) {
            fs.writeFileSync(filePath, content, 'utf8');
            console.log(`✅ Corregido: `+filePath);
            return true;
        } else {
            console.log(`⏭️  Sin cambios: `+filePath);
            return false;
        }
    } catch (error) {
        console.log(`❌ Error en `+filePath+`:`, error.message);
        return false;
    }
}

function findRouteFiles(dir) {
    const files = [];
    
    function searchDir(currentDir) {
        try {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !item.startsWith('.') && !item.includes('node_modules')) {
                    searchDir(fullPath);
                } else if (item === 'route.ts') {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            // Ignorar errores
        }
    }
    
    searchDir(dir);
    return files;
}

console.log('🔍 Buscando archivos route.ts...');
const routeFiles = findRouteFiles('./src/app/api');
console.log(`📁 Encontrados `+routeFiles.length+` archivos route.ts`);

let correctedCount = 0;
routeFiles.forEach(file => {
    if (fixRouteFile(file)) {
        correctedCount++;
    }
});

console.log(`\n🎉 Proceso completado!`);
console.log(`📊 Archivos corregidos: `+correctedCount+`/`+routeFiles.length);