@import "tailwindcss";
@import "tw-animate-css";

:root {
  --shark: #21252d;
  --outer-space: #2b333c;
  --blue-violet: #6762b3;
  --wild-sand: #f7f7f7;
  --empress: #7c777a;
  --cornflower-blue: #527ceb;
  --cerulean: #019fd2;
  --picton-blue: #48b0f7;
  --bright-turquoise: #10cfbd;
  --gallery: #f0f0f0;
}

html {
  scroll-behavior: smooth;
}

body {
  --sb-track-color: #232E33;
  --sb-thumb-color: #527ceb;
  --sb-size: 12px;
}

body::-webkit-scrollbar {
  width: var(--sb-size)
}

body::-webkit-scrollbar-track {
  background: var(--sb-track-color);
  border-radius: 8px;
}

body::-webkit-scrollbar-thumb {
  background: var(--sb-thumb-color);
  border-radius: 8px;
  
}

@supports not selector(::-webkit-scrollbar) {
  body {
    scrollbar-color: var(--sb-thumb-color)
                     var(--sb-track-color);
  }
}

/* src/app/globals.css - Agregar al final */

/* Fix para layout sin espacios feos */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

#__next {
  height: 100%;
}

/* Container principal del dashboard */
.dashboard-container {
  display: flex;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

/* Área de contenido principal */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #f9fafb;
}

/* Header fijo */
.header-fixed {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 1rem 1.5rem;
}

/* Área scrolleable del contenido */
.content-scrollable {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

/* Grid de tarjetas sin espacio extra */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* Tarjeta optimizada */
.stat-card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Footer compacto */
.footer-compact {
  flex-shrink: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 0.75rem 1.5rem;
  text-align: center;
}

/* Eliminar espacios innecesarios */
.no-extra-space {
  margin: 0 !important;
  padding-bottom: 0 !important;
}

/* Para móviles */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .content-scrollable {
    padding: 1rem;
  }
  
  .header-fixed {
    padding: 1rem;
  }
}

/* Scrollbar personalizado */
.content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.content-scrollable::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.content-scrollable::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.content-scrollable::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}