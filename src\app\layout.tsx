import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Roboto_Mono } from "next/font/google"; // <--- CAMBIO AQUÍ
import "./globals.css";

const InterSans = Inter({
  variable: "--font-Inter-sans",
  subsets: ["latin"],
});

// CAMBIO AQUÍ: Usamos una fuente real en lugar de 'monospace'
const InterMono = Roboto_Mono({
  variable: "--font-Inter-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Pagos Backstage Música",
  description: "Desarrolo por mon Caballero",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <body
        // La lógica de las clases no necesita cambios
        className={`${InterSans.variable} ${InterMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}