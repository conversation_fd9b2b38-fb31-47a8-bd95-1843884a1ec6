import bcrypt from 'bcryptjs'
import { nanoid } from 'nanoid'

export function generateTemporaryPassword(): string {
  return nanoid(8)
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10)
}

export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash)
}

export function validatePasswordStrength(password: string) {
  const errors: string[] = [];
  
  if (password.length < 8) errors.push('Mínimo 8 caracteres');
  if (!/[a-z]/.test(password)) errors.push('Una minúscula');
  if (!/[A-Z]/.test(password)) errors.push('Una mayúscula');  
  if (!/\d/.test(password)) errors.push('Un número');
  if (!/[!@#$%^&*]/.test(password)) errors.push('Un carácter especial');

  return {
    isValid: errors.length === 0,
    errors,
    strength: errors.length <= 1 ? 'strong' : errors.length <= 3 ? 'medium' : 'weak'
  };
}